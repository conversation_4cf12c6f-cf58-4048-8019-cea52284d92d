module BlockViews
  class HeroViews::Hero005Component < BaseComponent
    def view_template
      container do
        inner_container do
            div(class: "overflow-hidden sm:grid sm:grid-cols-2 items-center") do
              media_container(css: @block_object.media_alignment_class) do
                if @block_object.context == :admin
                  if @block_object.media.items.any?
                  render Ui::Image(@block_object.media.items.first.image, resize_options: { resize_to_fill: [1000, 800] }, classes: "w-full h-full object-cover")
                  else
                    render Ui::Image("https://picsum.photos/800/600", classes: "w-full h-full object-cover")
                  end
                else
                  div(data: { controller: "splide" }, class: "relative splide #{admin? ? 'z-0' : ''}") do
                    div(class: "splide__track") do
                      ul(class: "splide__list") do
                        @block_object.media.items.each do |item|
                          li(class: "splide__slide") do
                            render Ui::Image(item.image, resize_options: { resize_to_fill: [1000, 800] }, classes: "w-full h-full object-cover")
                          end
                        end
                      end
                    end
                  end
                end
              end

              content_container do
                div(class: "px-4") do
                  div(
                    class: "mx-auto flex max-w-2xl flex-col #{@block_object.gap_y_class}"
                  ) do
                    @block_object.controls.each do |control|
                      render control.component
                    end
                  end
                end
            end
          end
        end
      end
    end
  end
end
